package jp.co.torihada.fanme.modules.shop.usecases.item

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.Config
import jp.co.torihada.fanme.modules.shop.Util.Companion.getCurrentPrice
import jp.co.torihada.fanme.modules.shop.annotations.Maskable
import jp.co.torihada.fanme.modules.shop.models.*
import jp.co.torihada.fanme.modules.shop.models.Item as ItemModel
import jp.co.torihada.fanme.modules.shop.services.aws.S3
import jp.co.torihada.fanme.modules.shop.utils.MaskingProcessor

@ApplicationScoped
class GetItem {

    @Inject private lateinit var s3: S3

    @Inject private lateinit var config: Config

    @Inject private lateinit var userController: UserController

    data class Input(
        val itemId: Long,
        val creatorUid: String,
        val userUid: String,
        val applyMasking: Boolean,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class Item(
        val id: Long,
        val creatorAccountIdentity: String,
        @Maskable val name: String,
        @Maskable val description: String?,
        val thumbnailUri: String,
        val thumbnailFrom: Int = 0,
        val thumbnailBlurLevel: Int = 0,
        val thumbnailWatermarkLevel: Int = 1,
        val price: Int,
        val currentPrice: Int,
        val fileType: String,
        val available: Boolean,
        val awardProbabilities: List<AwardProbability>?,
        val isDuplicatedDigitalGachaItems: Boolean? = false,
        val itemType: Int = ItemType.DIGITAL_BUNDLE.value,
        val files: List<File>?,
        val samples: List<File>?,
        val benefit: BenefitData?,
        val tags: List<String>?,
        val itemOption: OptionData,
        val isPurchased: Boolean = false,
        val isCheckout: Boolean = false,
        val purchasedCount: Int = 0,
        val collectedUniqueItemsCount: Int = 0,
        val isCompleted: Boolean = false,
        val remainingUniquePullCount: Int? = null,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class File(
        val id: Long?,
        @Maskable val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val price: Int? = null,
        val currentPrice: Int? = null,
        val fileType: String,
        val size: Float,
        val duration: Int?,
        val isPurchased: Boolean = false,
        val isCheckout: Boolean = false,
        val itemThumbnailSelected: Boolean = false,
        val awardType: Int? = null,
        val isSecret: Boolean = false,
        val conditionType: Int = BenefitCondition.NONE.value,
        val receivedFileCount: Int? = 0,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class BenefitData(@Maskable val description: String, val files: List<File>?)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class OptionData(
        val isSingleSales: Boolean = false,
        val qtyTotal: Int?,
        val qtyPerUser: Int?,
        val remainingAmount: Int?,
        val forSale: ForSaleData?,
        val password: String?,
        val onSale: OnSaleData?,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class ForSaleData(val startAt: String?, val endAt: String?)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class OnSaleData(val discountRate: Float, val startAt: String?, val endAt: String?)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class AwardProbability(val awardType: Int, val probability: Int)

    fun execute(params: Input): Result<Item, FanmeException> {
        val item =
            ItemModel.findById(params.itemId) ?: return Err(ResourceNotFoundException("Item"))
        val shop =
            Shop.findByCreatorUid(params.creatorUid)
                ?: return Err(ResourceNotFoundException("Shop"))
        if (item.shop.id != shop.id) return Err(ResourceNotFoundException("Item"))

        val creator = userController.getUser(shop.creatorUid!!)
        val isSetPurchased = PurchasedItem.isPurchased(params.itemId, null, params.userUid)
        val isSetCheckout = PurchasedItem.isCheckout(params.itemId, null, params.userUid)
        val files =
            item.files
                .takeIf { it.isNotEmpty() }
                ?.let {
                    if (item.itemType == ItemType.DIGITAL_GACHA) {
                        it.sortedBy { file -> file.id }
                    } else {
                        item.sortedFiles()
                    }
                }
        // デジタル商品の場合、filesがnullまたは空の場合はエラー
        if (item.isDigital && files.isNullOrEmpty()) {
            return Err(ResourceNotFoundException("ItemFile"))
        }
        val gachaReceivedFiles =
            GachaReceivedFile.findByPurchaserUidAndItemId(params.userUid, item.id!!)
        val gachaReceivedFileCounts = gachaReceivedFiles.groupBy { it?.itemFile?.id }
        val gachaReceivedFileCountsMap = gachaReceivedFileCounts.mapValues { it.value.size }
        val option = item.option ?: return Err(ResourceNotFoundException("ItemOption"))
        val forSale =
            ForSaleData(
                startAt = option.forSaleStartAt?.toString(),
                endAt = option.forSaleEndAt?.toString(),
            )
        val benefit = item.benefit
        val benefitFiles = benefit?.files?.sortedBy { it.id } ?: emptyList()
        val samples = item.samples.sortedBy { it.id }
        val onSale = item.onSale
        val tags = item.tags.map { it.tag.tag }
        val remainingAmount =
            if (option.qtyTotal != null)
                option.qtyTotal!! - PurchasedItem.countPurchasedItem(params.itemId)
            else null

        val purchasedCount =
            PurchasedItem.countPurchasedItemByPurchaserUid(
                itemId = item.id!!,
                purchaserUid = params.userUid,
            )

        val collectedUniqueItemsCount =
            GachaReceivedFile.findByPurchaserUidAndItemId(params.userUid, item.id!!)
                .map { it?.itemFile?.id }
                .distinct()
                .size

        val totalUniqueItemsCount = files?.size ?: 0
        val isCompleted =
            totalUniqueItemsCount == collectedUniqueItemsCount && totalUniqueItemsCount > 0

        val isDuplicatedDigitalGachaItems = item.gachaItem?.isDuplicated ?: false
        val remainingUniquePullCount =
            if (!isDuplicatedDigitalGachaItems) {
                totalUniqueItemsCount - collectedUniqueItemsCount
            } else {
                null
            }

        val output =
            Item(
                id = item.id!!,
                creatorAccountIdentity = creator.accountIdentity ?: "",
                name = item.name,
                description = item.description,
                thumbnailUri = item.thumbnailUri,
                thumbnailFrom = item.thumbnailFrom,
                thumbnailBlurLevel = item.thumbnailBlurLevel,
                thumbnailWatermarkLevel = item.thumbnailWatermarkLevel,
                price = item.price,
                currentPrice = getCurrentPrice(item),
                fileType = item.fileType.toString(2).padStart(3, '0').reversed(),
                available = item.available,
                awardProbabilities =
                    item.gachaItem?.gachaProbability?.map {
                        AwardProbability(
                            awardType = it.awardType.value,
                            probability = it.probability,
                        )
                    },
                isDuplicatedDigitalGachaItems = isDuplicatedDigitalGachaItems,
                itemType = item.itemType.value,
                files =
                    files?.map {
                        File(
                            id = it.id,
                            name = it.name,
                            objectUri = s3.getObjectUri(it.objectUri!!),
                            thumbnailUri = s3.getObjectUri(it.thumbnailUri!!),
                            price = it.price,
                            currentPrice = getCurrentPrice(it),
                            fileType = it.fileType,
                            size = it.size,
                            duration = it.duration,
                            isPurchased =
                                if (isSetPurchased) {
                                    true
                                } else {
                                    PurchasedItem.isPurchased(item.id!!, it.id, params.userUid)
                                },
                            isCheckout =
                                if (isSetCheckout) {
                                    true
                                } else {
                                    PurchasedItem.isCheckout(item.id!!, it.id, params.userUid)
                                },
                            itemThumbnailSelected = it.itemThumbnailSelected,
                            awardType = it.gachaItemFile?.awardType?.value,
                            isSecret = it.gachaItemFile?.isSecret ?: false,
                            receivedFileCount = gachaReceivedFileCountsMap?.get(it.id) ?: 0,
                        )
                    },
                samples =
                    if (samples.isNotEmpty()) {
                        samples.map { sample ->
                            File(
                                id = sample.id,
                                name = sample.name,
                                objectUri = sample.objectUri,
                                thumbnailUri = sample.thumbnailUri,
                                fileType = sample.fileType,
                                size = sample.size,
                                duration = sample.duration,
                            )
                        }
                    } else {
                        null
                    },
                benefit =
                    if (benefit != null) {
                        BenefitData(
                            description = benefit.description ?: "",
                            files =
                                benefitFiles.map { benefitFile ->
                                    File(
                                        id = benefitFile.id,
                                        name = benefitFile.name,
                                        objectUri = s3.getObjectUri(benefitFile.objectUri),
                                        thumbnailUri =
                                            s3.getObjectUri(benefitFile.thumbnailUri ?: ""),
                                        fileType = benefitFile.fileType,
                                        size = benefitFile.size,
                                        duration = benefitFile.duration,
                                        conditionType = benefitFile.benefit.conditionType.value,
                                        receivedFileCount =
                                            gachaReceivedFileCountsMap?.get(benefitFile.id) ?: 0,
                                    )
                                },
                        )
                    } else {
                        null
                    },
                tags = tags,
                itemOption =
                    OptionData(
                        isSingleSales = option.isSingleSales,
                        qtyTotal = option.qtyTotal,
                        qtyPerUser = option.qtyPerUser,
                        remainingAmount = remainingAmount,
                        forSale = forSale,
                        password = option.password,
                        onSale =
                            if (onSale != null) {
                                OnSaleData(
                                    discountRate = onSale.discountRate,
                                    startAt = onSale.startAt?.toString(),
                                    endAt = onSale.endAt?.toString(),
                                )
                            } else {
                                null
                            },
                    ),
                isPurchased = isSetPurchased,
                isCheckout = isSetCheckout,
                purchasedCount = purchasedCount,
                collectedUniqueItemsCount = collectedUniqueItemsCount,
                isCompleted = isCompleted,
                remainingUniquePullCount = remainingUniquePullCount,
            )

        return Ok(if (params.applyMasking) MaskingProcessor.process(output) else output)
    }
}
